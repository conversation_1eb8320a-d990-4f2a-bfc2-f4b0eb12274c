"""
K230显示屏用户界面模块
提供清晰的测量结果显示和状态指示
"""

import time

# K230平台导入1
import image  # K230的image模块
K230_PLATFORM = True

class DisplayUI:
    """
    K230显示屏用户界面类
    负责测量结果的可视化显示
    """

    # 颜色定义（RGB565格式）
    COLORS = {
        'white': (255, 255, 255),
        'black': (0, 0, 0),
        'red': (255, 0, 0),
        'green': (0, 255, 0),
        'blue': (0, 0, 255),
        'yellow': (255, 255, 0),
        'cyan': (0, 255, 255),
        'magenta': (255, 0, 255),
        'orange': (255, 165, 0),
        'gray': (128, 128, 128)
    }

    # 状态颜色映射
    STATUS_COLORS = {
        'idle': 'yellow',
        'calibrating': 'cyan',
        'measuring': 'orange',
        'ready': 'green',
        'error': 'red'
    }

    # 形状颜色映射
    SHAPE_COLORS = {
        'circle': 'blue',
        'triangle': 'green',
        'square': 'magenta',
        'unknown': 'gray'
    }

    def __init__(self, screen_width=1920, screen_height=1080):
        """
        初始化显示界面

        参数:
            screen_width: int, 屏幕宽度
            screen_height: int, 屏幕高度
        """
        self.screen_width = screen_width
        self.screen_height = screen_height

        # 根据屏幕尺寸调整布局参数
        if screen_width <= 640:  # 小屏幕布局（2.4寸屏）
            self.title_y = 10           # 标题Y位置
            self.status_y = 35          # 状态显示Y位置
            self.measurement_y = 70     # 测量结果Y位置
            self.info_y = 180           # 信息显示Y位置
            self.help_y = 250           # 帮助信息Y位置

            self.left_margin = 10       # 左边距
            self.right_margin = 10      # 右边距

            # 小屏幕字体大小
            self.title_font_size = 16
            self.status_font_size = 12
            self.measurement_font_size = 14
            self.info_font_size = 10
            self.help_font_size = 8
        else:  # 大屏幕布局
            self.title_y = 30           # 标题Y位置
            self.status_y = 80          # 状态显示Y位置
            self.measurement_y = 150    # 测量结果Y位置
            self.info_y = 400           # 信息显示Y位置
            self.help_y = 600           # 帮助信息Y位置

            self.left_margin = 50       # 左边距
            self.right_margin = 50      # 右边距

            # 大屏幕字体大小
            self.title_font_size = 32
            self.status_font_size = 24
            self.measurement_font_size = 28
            self.info_font_size = 20
            self.help_font_size = 16

        print("DisplayUI初始化完成")

    def draw_title(self, img):
        """绘制标题"""
        title = "单目视觉测量装置"
        img.draw_string_advanced(
            self.left_margin, self.title_y,
            self.title_font_size, title,
            color=self.COLORS['white']
        )

        # 绘制分割线
        line_y = self.title_y + 40
        img.draw_line(
            self.left_margin, line_y,
            self.screen_width - self.right_margin, line_y,
            color=self.COLORS['gray'], thickness=2
        )

    def draw_system_status(self, img, status, focal_length=None):
        """
        绘制系统状态

        参数:
            img: 图像对象
            status: str, 系统状态
            focal_length: float, 焦距值(可选)
        """
        # 状态显示
        status_text = f"系统状态: {self._get_status_text(status)}"
        status_color = self.STATUS_COLORS.get(status, 'white')

        img.draw_string_advanced(
            self.left_margin, self.status_y,
            self.status_font_size, status_text,
            color=self.COLORS[status_color]
        )

        # 标定信息
        focal_x_offset = 200 if self.screen_width <= 640 else 300  # 小屏幕调整偏移
        if focal_length is not None:
            focal_text = f"焦距: {focal_length:.1f}"
            img.draw_string_advanced(
                self.left_margin + focal_x_offset, self.status_y,
                self.status_font_size, focal_text,
                color=self.COLORS['green']
            )
        else:
            img.draw_string_advanced(
                self.left_margin + focal_x_offset, self.status_y,
                self.status_font_size, "未标定",
                color=self.COLORS['red']
            )

    def draw_measurement_results(self, img, measurement_data):
        """
        绘制测量结果

        参数:
            img: 图像对象
            measurement_data: dict, 测量数据 {distance_cm, shape_type, size_cm, timestamp}
        """
        if not measurement_data:
            # 无测量数据时显示提示
            img.draw_string_advanced(
                self.left_margin, self.measurement_y,
                self.measurement_font_size, "暂无测量数据",
                color=self.COLORS['gray']
            )
            return

        distance = measurement_data.get('distance_cm', 0)
        shape = measurement_data.get('shape_type', 'unknown')
        size = measurement_data.get('size_cm', 0)
        timestamp = measurement_data.get('timestamp', 0)

        # 距离显示
        distance_text = f"距离 D: {distance:.1f} cm"
        img.draw_string_advanced(
            self.left_margin, self.measurement_y,
            self.measurement_font_size, distance_text,
            color=self.COLORS['yellow']
        )

        # 形状显示
        line_spacing = 20 if self.screen_width <= 640 else 40  # 小屏幕调整行间距
        shape_text = f"形状: {self._get_shape_text(shape)}"
        shape_color = self.SHAPE_COLORS.get(shape, 'white')
        img.draw_string_advanced(
            self.left_margin, self.measurement_y + line_spacing,
            self.measurement_font_size, shape_text,
            color=self.COLORS[shape_color]
        )

        # 尺寸显示
        size_text = f"尺寸 x: {size:.1f} cm"
        img.draw_string_advanced(
            self.left_margin, self.measurement_y + line_spacing * 2,
            self.measurement_font_size, size_text,
            color=self.COLORS['cyan']
        )

        # 时间戳显示
        if timestamp > 0:
            time_text = f"测量时间: {self._format_timestamp(timestamp)}"
            img.draw_string_advanced(
                self.left_margin, self.measurement_y + line_spacing * 3,
                self.info_font_size, time_text,
                color=self.COLORS['white']
            )

    def draw_measurement_accuracy(self, img, distance, size):
        """
        绘制测量精度指示

        参数:
            img: 图像对象
            distance: float, 距离值
            size: float, 尺寸值
        """
        # 距离精度检查（要求≤5cm误差）
        distance_status = "✓" if distance > 0 else "?"
        distance_color = 'green' if distance > 0 else 'gray'

        distance_accuracy_text = f"距离精度: {distance_status} (要求: ±5cm)"
        img.draw_string_advanced(
            self.left_margin, self.info_y,
            self.info_font_size, distance_accuracy_text,
            color=self.COLORS[distance_color]
        )

        # 尺寸精度检查（要求≤1cm误差）
        size_status = "✓" if size > 0 else "?"
        size_color = 'green' if size > 0 else 'gray'

        size_accuracy_text = f"尺寸精度: {size_status} (要求: ±1cm)"
        img.draw_string_advanced(
            self.left_margin, self.info_y + 30,
            self.info_font_size, size_accuracy_text,
            color=self.COLORS[size_color]
        )

    def draw_operation_guide(self, img, status):
        """
        绘制操作指南

        参数:
            img: 图像对象
            status: str, 当前系统状态
        """
        guide_texts = self._get_operation_guide(status)

        for i, text in enumerate(guide_texts):
            img.draw_string_advanced(
                self.left_margin, self.help_y + i * 25,
                self.help_font_size, text,
                color=self.COLORS['white']
            )

    def draw_fps_info(self, img, fps):
        """
        绘制帧率信息

        参数:
            img: 图像对象
            fps: float, 帧率
        """
        fps_text = f"FPS: {fps:.1f}"
        fps_x_pos = self.screen_width - (80 if self.screen_width <= 640 else 150)  # 小屏幕调整位置
        fps_y_pos = 15 if self.screen_width <= 640 else 30  # 小屏幕调整Y位置
        img.draw_string_advanced(
            fps_x_pos, fps_y_pos,
            self.info_font_size, fps_text,
            color=self.COLORS['green']
        )

    def draw_complete_ui(self, img, system_data):
        """
        绘制完整的用户界面

        参数:
            img: 图像对象
            system_data: dict, 系统数据
                - status: str, 系统状态
                - focal_length: float, 焦距
                - measurement: dict, 测量结果
                - fps: float, 帧率
        """
        # 清空背景（可选，如果需要半透明效果可以跳过）
        # img.clear()

        # 绘制标题
        self.draw_title(img)

        # 绘制系统状态
        status = system_data.get('status', 'idle')
        focal_length = system_data.get('focal_length')
        self.draw_system_status(img, status, focal_length)

        # 绘制测量结果
        measurement = system_data.get('measurement')
        self.draw_measurement_results(img, measurement)

        # 绘制精度指示
        if measurement:
            distance = measurement.get('distance_cm', 0)
            size = measurement.get('size_cm', 0)
            self.draw_measurement_accuracy(img, distance, size)

        # 绘制操作指南
        self.draw_operation_guide(img, status)

        # 绘制帧率
        fps = system_data.get('fps', 0)
        self.draw_fps_info(img, fps)

    def _get_status_text(self, status):
        """获取状态显示文本"""
        status_map = {
            'idle': '空闲',
            'calibrating': '标定中',
            'measuring': '测量中',
            'ready': '就绪',
            'error': '错误'
        }
        return status_map.get(status, '未知')

    def _get_shape_text(self, shape):
        """获取形状显示文本"""
        shape_map = {
            'circle': '圆形',
            'triangle': '三角形',
            'square': '正方形',
            'unknown': '未知'
        }
        return shape_map.get(shape, '未知')

    def _get_operation_guide(self, status):
        """获取操作指南文本"""
        guides = {
            'idle': [
                "操作指南:",
                "1. 将A4纸放置在150cm处",
                "2. 按下按键进行标定",
                "3. 等待标定完成"
            ],
            'calibrating': [
                "标定中...",
                "请保持A4纸位置稳定",
                "等待标定完成"
            ],
            'ready': [
                "操作指南:",
                "1. 放置目标物（圆形/三角形/正方形）",
                "2. 按下按键开始测量",
                "3. 查看测量结果"
            ],
            'measuring': [
                "测量中...",
                "请保持目标物位置稳定",
                "等待测量完成"
            ],
            'error': [
                "发生错误",
                "请检查设备连接",
                "重新启动系统"
            ]
        }
        return guides.get(status, ["未知状态"])

    def _format_timestamp(self, timestamp):
        """格式化时间戳"""
        try:
            # K230环境下的时间格式化
            return f"{timestamp}ms"
        except:
            return f"{timestamp}"



