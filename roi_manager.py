"""
ROI(感兴趣区域)管理模块
基于K230开发板的图像处理模块实现ROI区域管理功能
"""

import math

# K230平台导入
import image  # K230的image模块
K230_PLATFORM = True


class ROIManager:
    """
    ROI(感兴趣区域)管理类
    负责ROI参数管理、坐标转换、图像裁剪等功能
    """
    
    def __init__(self):
        """
        初始化ROI管理器
        """
        # ROI启用状态
        self.roi_enabled = False            # ROI功能启用标志，默认禁用
        
        # ROI参数(归一化坐标，范围0.0-1.0)
        self.roi_x_ratio = 0.0              # ROI左上角X坐标比例
        self.roi_y_ratio = 0.0              # ROI左上角Y坐标比例
        self.roi_width_ratio = 1.0          # ROI宽度比例
        self.roi_height_ratio = 1.0         # ROI高度比例
        
        # ROI约束参数
        self.MIN_ROI_SIZE = 100             # ROI最小尺寸(像素)，确保有效检测区域
        self.MAX_ROI_RATIO = 1.0            # ROI最大比例，不能超过图像边界
        self.MIN_ROI_RATIO = 0.1            # ROI最小比例，确保有意义的检测区域
        self.ROI_STEP_SIZE = 0.05           # ROI调节步长，用于按键调节
        
        # ROI状态缓存
        self.last_roi_pixels = None         # 最后计算的像素坐标缓存
        self.last_image_size = None         # 最后处理的图像尺寸
        
        print("ROIManager初始化完成")
        print("ROI默认状态: 禁用, 全图像区域")
    
    def set_roi_params(self, x_ratio=None, y_ratio=None, width_ratio=None, height_ratio=None, enabled=None):
        """
        动态设置ROI参数
        
        参数:
            x_ratio: float, ROI左上角X坐标比例(0.0-1.0)
            y_ratio: float, ROI左上角Y坐标比例(0.0-1.0)
            width_ratio: float, ROI宽度比例(0.0-1.0)
            height_ratio: float, ROI高度比例(0.0-1.0)
            enabled: bool, ROI启用状态
        """
        # 仅更新非None的参数，保持其他参数不变
        if x_ratio is not None:
            self.roi_x_ratio = max(0.0, min(x_ratio, 1.0))         # 限制在有效范围内
        if y_ratio is not None:
            self.roi_y_ratio = max(0.0, min(y_ratio, 1.0))         # 限制在有效范围内
        if width_ratio is not None:
            self.roi_width_ratio = max(self.MIN_ROI_RATIO, min(width_ratio, self.MAX_ROI_RATIO))
        if height_ratio is not None:
            self.roi_height_ratio = max(self.MIN_ROI_RATIO, min(height_ratio, self.MAX_ROI_RATIO))
        if enabled is not None:
            self.roi_enabled = enabled
        
        # 验证ROI参数的有效性
        if not self._validate_roi_bounds():
            print("ROI参数调整: 自动修正超出边界的参数")
            self._fix_roi_bounds()
        
        # 清除缓存，强制重新计算
        self.last_roi_pixels = None
        self.last_image_size = None
        
        print("ROI参数已更新: 启用={}, 位置=({:.2f}, {:.2f}), 尺寸=({:.2f}, {:.2f})".format(
            self.roi_enabled, self.roi_x_ratio, self.roi_y_ratio, 
            self.roi_width_ratio, self.roi_height_ratio))
    
    def apply_roi(self, img):
        """
        对图像应用ROI裁剪
        
        参数:
            img: 输入图像对象
            
        返回:
            裁剪后的图像对象，如果ROI禁用则返回原图像
        """
        try:
            if img is None:
                print("ROI应用失败: 输入图像为空")
                return None
            
            # 如果ROI功能禁用，直接返回原图像
            if not self.roi_enabled:
                return img
            
            # 获取图像尺寸
            img_width = img.width()
            img_height = img.height()
            
            # 计算ROI像素坐标
            roi_pixels = self.get_roi_pixels(img_width, img_height)
            if roi_pixels is None:
                print("ROI应用失败: 像素坐标计算错误")
                return img
            
            roi_x, roi_y, roi_w, roi_h = roi_pixels
            
            # 验证ROI尺寸
            if roi_w < self.MIN_ROI_SIZE or roi_h < self.MIN_ROI_SIZE:
                print("ROI应用警告: ROI尺寸过小({}x{}), 使用原图像".format(roi_w, roi_h))
                return img
            
            # 使用K230的copy方法进行ROI裁剪
            try:
                roi_img = img.copy(roi=(roi_x, roi_y, roi_w, roi_h))
                print("ROI裁剪成功: 区域({}, {}, {}, {}), 输出尺寸{}x{}".format(
                    roi_x, roi_y, roi_w, roi_h, roi_img.width(), roi_img.height()))
                return roi_img
            except Exception as e:
                print("ROI裁剪失败: {}".format(e))
                return img
                
        except Exception as e:
            print("ROI应用异常: {}".format(e))
            return img
    
    def get_roi_pixels(self, img_width, img_height):
        """
        将归一化坐标转换为像素坐标
        
        参数:
            img_width: int, 图像宽度
            img_height: int, 图像高度
            
        返回:
            tuple: (x, y, width, height) 像素坐标，或None
        """
        try:
            # 检查输入参数
            if img_width <= 0 or img_height <= 0:
                print("像素坐标转换失败: 图像尺寸无效")
                return None
            
            # 检查缓存是否有效
            current_size = (img_width, img_height)
            if (self.last_roi_pixels is not None and 
                self.last_image_size == current_size):
                return self.last_roi_pixels
            
            # 计算像素坐标
            roi_x = int(self.roi_x_ratio * img_width)
            roi_y = int(self.roi_y_ratio * img_height)
            roi_w = int(self.roi_width_ratio * img_width)
            roi_h = int(self.roi_height_ratio * img_height)
            
            # 边界检查和修正
            roi_x = max(0, min(roi_x, img_width - 1))
            roi_y = max(0, min(roi_y, img_height - 1))
            roi_w = max(1, min(roi_w, img_width - roi_x))
            roi_h = max(1, min(roi_h, img_height - roi_y))
            
            # 缓存结果
            self.last_roi_pixels = (roi_x, roi_y, roi_w, roi_h)
            self.last_image_size = current_size
            
            return self.last_roi_pixels
            
        except Exception as e:
            print("像素坐标转换异常: {}".format(e))
            return None
    
    def validate_roi(self, img_width=None, img_height=None):
        """
        验证ROI参数的有效性
        
        参数:
            img_width: int, 图像宽度(可选)
            img_height: int, 图像高度(可选)
            
        返回:
            bool: ROI参数是否有效
        """
        try:
            # 检查归一化坐标范围
            if not (0.0 <= self.roi_x_ratio <= 1.0 and 
                    0.0 <= self.roi_y_ratio <= 1.0 and
                    0.0 < self.roi_width_ratio <= 1.0 and
                    0.0 < self.roi_height_ratio <= 1.0):
                return False
            
            # 检查ROI边界
            if not self._validate_roi_bounds():
                return False
            
            # 如果提供了图像尺寸，检查像素尺寸
            if img_width is not None and img_height is not None:
                roi_pixels = self.get_roi_pixels(img_width, img_height)
                if roi_pixels is None:
                    return False
                
                _, _, roi_w, roi_h = roi_pixels
                if roi_w < self.MIN_ROI_SIZE or roi_h < self.MIN_ROI_SIZE:
                    return False
            
            return True
            
        except Exception as e:
            print("ROI验证异常: {}".format(e))
            return False
    
    def reset_roi(self):
        """
        重置ROI为全图像区域
        """
        self.roi_enabled = False
        self.roi_x_ratio = 0.0
        self.roi_y_ratio = 0.0
        self.roi_width_ratio = 1.0
        self.roi_height_ratio = 1.0
        
        # 清除缓存
        self.last_roi_pixels = None
        self.last_image_size = None
        
        print("ROI已重置为全图像区域")
    
    def get_roi_status(self):
        """
        获取当前ROI状态信息
        
        返回:
            dict: ROI状态信息字典
        """
        return {
            'enabled': self.roi_enabled,
            'x_ratio': self.roi_x_ratio,
            'y_ratio': self.roi_y_ratio,
            'width_ratio': self.roi_width_ratio,
            'height_ratio': self.roi_height_ratio,
            'min_size': self.MIN_ROI_SIZE,
            'step_size': self.ROI_STEP_SIZE
        }
    
    def adjust_roi_position(self, dx_ratio, dy_ratio):
        """
        调整ROI位置
        
        参数:
            dx_ratio: float, X方向移动比例
            dy_ratio: float, Y方向移动比例
        """
        new_x = self.roi_x_ratio + dx_ratio
        new_y = self.roi_y_ratio + dy_ratio
        
        # 确保ROI不超出图像边界
        new_x = max(0.0, min(new_x, 1.0 - self.roi_width_ratio))
        new_y = max(0.0, min(new_y, 1.0 - self.roi_height_ratio))
        
        self.set_roi_params(x_ratio=new_x, y_ratio=new_y)
    
    def adjust_roi_size(self, dw_ratio, dh_ratio):
        """
        调整ROI尺寸
        
        参数:
            dw_ratio: float, 宽度变化比例
            dh_ratio: float, 高度变化比例
        """
        new_w = self.roi_width_ratio + dw_ratio
        new_h = self.roi_height_ratio + dh_ratio
        
        # 限制尺寸范围
        new_w = max(self.MIN_ROI_RATIO, min(new_w, 1.0 - self.roi_x_ratio))
        new_h = max(self.MIN_ROI_RATIO, min(new_h, 1.0 - self.roi_y_ratio))
        
        self.set_roi_params(width_ratio=new_w, height_ratio=new_h)
    
    def _validate_roi_bounds(self):
        """
        验证ROI边界是否在图像范围内
        
        返回:
            bool: 边界是否有效
        """
        return (self.roi_x_ratio + self.roi_width_ratio <= 1.0 and
                self.roi_y_ratio + self.roi_height_ratio <= 1.0)
    
    def _fix_roi_bounds(self):
        """
        修正超出边界的ROI参数
        """
        # 修正宽度
        if self.roi_x_ratio + self.roi_width_ratio > 1.0:
            self.roi_width_ratio = 1.0 - self.roi_x_ratio
        
        # 修正高度
        if self.roi_y_ratio + self.roi_height_ratio > 1.0:
            self.roi_height_ratio = 1.0 - self.roi_y_ratio
        
        # 确保最小尺寸
        self.roi_width_ratio = max(self.MIN_ROI_RATIO, self.roi_width_ratio)
        self.roi_height_ratio = max(self.MIN_ROI_RATIO, self.roi_height_ratio)
