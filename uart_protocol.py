"""
单目视觉测量装置串口通信协议
支持测量结果数据传输，包括距离D、形状类型、尺寸x等信息
"""

import struct
import time

class UartProtocol:
    """
    串口通信协议类
    负责测量数据的编码、解码和传输
    """
    
    # 协议常量定义
    FRAME_HEADER = 0xAA        # 协议帧头
    FRAME_TAIL = 0x55          # 协议帧尾
    
    # 任务帧头定义
    TASK_MEASUREMENT = 0xA1    # 测量数据任务帧头
    TASK_STATUS = 0xA2         # 状态信息任务帧头
    TASK_CALIBRATION = 0xA3    # 标定信息任务帧头
    TASK_ERROR = 0xAE          # 错误信息任务帧头
    
    # 形状类型编码
    SHAPE_CODES = {
        "circle": 0x01,        # 圆形
        "triangle": 0x02,      # 三角形
        "square": 0x03,        # 正方形
        "unknown": 0x00        # 未知形状
    }
    
    # 形状类型解码
    SHAPE_NAMES = {v: k for k, v in SHAPE_CODES.items()}
    
    # 系统状态编码
    STATUS_CODES = {
        "idle": 0x00,          # 空闲状态
        "calibrating": 0x01,   # 标定中
        "measuring": 0x02,     # 测量中
        "ready": 0x03,         # 准备就绪
        "error": 0xFF          # 错误状态
    }
    
    def __init__(self):
        """初始化协议处理器"""
        self.sequence_number = 0  # 序列号，用于数据包跟踪
        
    def encode_measurement_data(self, distance_cm, shape_type, size_cm, timestamp=None):
        """
        编码测量数据
        
        参数:
            distance_cm: float, 距离(厘米)
            shape_type: str, 形状类型 ('circle', 'triangle', 'square')
            size_cm: float, 尺寸(厘米)
            timestamp: int, 时间戳(毫秒)，可选
            
        返回:
            bytes: 编码后的数据包
        """
        try:
            # 获取时间戳
            if timestamp is None:
                timestamp = int(time.time() * 1000) & 0xFFFFFFFF  # 32位时间戳
            
            # 数据转换和范围检查
            distance_raw = max(0, min(int(distance_cm * 10), 0xFFFF))  # 距离*10，限制在16位
            size_raw = max(0, min(int(size_cm * 10), 0xFFFF))          # 尺寸*10，限制在16位
            shape_code = self.SHAPE_CODES.get(shape_type, 0x00)        # 形状编码
            
            # 序列号递增
            self.sequence_number = (self.sequence_number + 1) & 0xFF
            
            # 数据包结构：
            # 距离(2字节) + 形状(1字节) + 尺寸(2字节) + 序列号(1字节) + 时间戳(4字节)
            data_payload = struct.pack('<HBHBI', 
                                     distance_raw,      # 距离 (uint16)
                                     shape_code,        # 形状 (uint8)
                                     size_raw,          # 尺寸 (uint16)
                                     self.sequence_number,  # 序列号 (uint8)
                                     timestamp)         # 时间戳 (uint32)
            
            return data_payload
            
        except Exception as e:
            print(f"测量数据编码错误: {e}")
            return b''
    
    def decode_measurement_data(self, data_payload):
        """
        解码测量数据
        
        参数:
            data_payload: bytes, 数据载荷
            
        返回:
            dict: 解码后的测量数据，包含distance_cm, shape_type, size_cm, sequence, timestamp
        """
        try:
            if len(data_payload) < 10:  # 最小数据包长度
                return None
                
            # 解包数据
            distance_raw, shape_code, size_raw, sequence, timestamp = struct.unpack('<HBHBI', data_payload[:10])
            
            # 数据转换
            distance_cm = distance_raw / 10.0
            size_cm = size_raw / 10.0
            shape_type = self.SHAPE_NAMES.get(shape_code, "unknown")
            
            return {
                'distance_cm': distance_cm,
                'shape_type': shape_type,
                'size_cm': size_cm,
                'sequence': sequence,
                'timestamp': timestamp
            }
            
        except Exception as e:
            print(f"测量数据解码错误: {e}")
            return None
    
    def encode_status_data(self, status, focal_length=None, error_code=None):
        """
        编码状态数据
        
        参数:
            status: str, 系统状态
            focal_length: float, 焦距值(可选)
            error_code: int, 错误代码(可选)
            
        返回:
            bytes: 编码后的状态数据包
        """
        try:
            status_code = self.STATUS_CODES.get(status, 0xFF)
            
            # 焦距数据（如果提供）
            focal_raw = int(focal_length * 100) if focal_length else 0  # 焦距*100
            focal_raw = max(0, min(focal_raw, 0xFFFFFFFF))  # 限制在32位
            
            # 错误代码
            error_code = error_code or 0
            
            # 状态数据包结构：状态(1字节) + 焦距(4字节) + 错误代码(1字节)
            status_payload = struct.pack('<BIB', status_code, focal_raw, error_code)
            
            return status_payload
            
        except Exception as e:
            print(f"状态数据编码错误: {e}")
            return b''
    
    def create_protocol_frame(self, task_header, data_payload):
        """
        创建完整的协议帧
        
        参数:
            task_header: int, 任务帧头
            data_payload: bytes, 数据载荷
            
        返回:
            bytes: 完整的协议帧
        """
        try:
            # 计算校验和（简单异或校验）
            checksum = task_header
            for byte in data_payload:
                checksum ^= byte
            checksum &= 0xFF
            
            # 构建完整帧：帧头 + 任务帧头 + 数据长度 + 数据载荷 + 校验和 + 帧尾
            frame = bytearray()
            frame.append(self.FRAME_HEADER)     # 协议帧头
            frame.append(task_header)           # 任务帧头
            frame.append(len(data_payload))     # 数据长度
            frame.extend(data_payload)          # 数据载荷
            frame.append(checksum)              # 校验和
            frame.append(self.FRAME_TAIL)       # 协议帧尾
            
            return bytes(frame)
            
        except Exception as e:
            print(f"协议帧创建错误: {e}")
            return b''
    
    def parse_protocol_frame(self, frame_data):
        """
        解析协议帧

        参数:
            frame_data: bytes, 接收到的帧数据

        返回:
            dict: 解析结果，包含task_header, data_payload, valid等字段
        """
        try:
            if len(frame_data) < 6:  # 最小帧长度
                return {'valid': False, 'error': '帧长度不足'}

            # 检查帧头和帧尾
            if frame_data[0] != self.FRAME_HEADER or frame_data[-1] != self.FRAME_TAIL:
                return {'valid': False, 'error': '帧头或帧尾错误'}

            # 提取字段
            task_header = frame_data[1]
            data_length = frame_data[2]

            # 检查数据长度：帧头(1) + 任务帧头(1) + 长度(1) + 数据(N) + 校验和(1) + 帧尾(1) = 5 + N
            expected_length = 5 + data_length
            if len(frame_data) != expected_length:
                return {'valid': False, 'error': f'数据长度不匹配: 期望{expected_length}, 实际{len(frame_data)}'}

            # 提取数据载荷和校验和
            data_payload = frame_data[3:3+data_length]
            received_checksum = frame_data[3+data_length]

            # 计算校验和
            calculated_checksum = task_header
            for byte in data_payload:
                calculated_checksum ^= byte
            calculated_checksum &= 0xFF

            # 验证校验和
            if received_checksum != calculated_checksum:
                return {'valid': False, 'error': f'校验和错误: 期望{calculated_checksum:02X}, 实际{received_checksum:02X}'}

            return {
                'valid': True,
                'task_header': task_header,
                'data_payload': data_payload,
                'data_length': data_length
            }

        except Exception as e:
            return {'valid': False, 'error': f'解析异常: {e}'}
    
    def create_measurement_frame(self, distance_cm, shape_type, size_cm):
        """
        创建测量数据帧（便捷方法）
        
        参数:
            distance_cm: float, 距离(厘米)
            shape_type: str, 形状类型
            size_cm: float, 尺寸(厘米)
            
        返回:
            bytes: 完整的测量数据帧
        """
        data_payload = self.encode_measurement_data(distance_cm, shape_type, size_cm)
        return self.create_protocol_frame(self.TASK_MEASUREMENT, data_payload)
    
    def create_status_frame(self, status, focal_length=None):
        """
        创建状态数据帧（便捷方法）
        
        参数:
            status: str, 系统状态
            focal_length: float, 焦距值(可选)
            
        返回:
            bytes: 完整的状态数据帧
        """
        data_payload = self.encode_status_data(status, focal_length)
        return self.create_protocol_frame(self.TASK_STATUS, data_payload)
    
    def get_protocol_info(self):
        """
        获取协议信息
        
        返回:
            dict: 协议信息字典
        """
        return {
            'protocol_version': '1.0',
            'frame_header': f'0x{self.FRAME_HEADER:02X}',
            'frame_tail': f'0x{self.FRAME_TAIL:02X}',
            'task_headers': {
                'measurement': f'0x{self.TASK_MEASUREMENT:02X}',
                'status': f'0x{self.TASK_STATUS:02X}',
                'calibration': f'0x{self.TASK_CALIBRATION:02X}',
                'error': f'0x{self.TASK_ERROR:02X}'
            },
            'shape_codes': self.SHAPE_CODES,
            'status_codes': self.STATUS_CODES
        }



